<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt Details UI Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="css/bootstrap.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/receipt-details-modal.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="mb-0">Receipt Details UI 測試</h3>
                    </div>
                    <div class="card-body text-center">
                        <p class="mb-4">點擊下方按鈕測試新的 Receipt Details UI 設計</p>
                        <button type="button" class="btn btn-primary btn-lg" onclick="showTestModal()">
                            📄 顯示收據詳情
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 測試數據
        const testReceipt = {
            id: 1,
            receipt_number: 'KMS-2024-001',
            customer_name: '張小明',
            customer_phone: '0912-345-678',
            customer_email: '<EMAIL>',
            customer_address: '台北市信義區信義路五段7號',
            created_at: '2024-01-15T10:30:00Z',
            payment_method: '信用卡',
            subtotal: 25000.00,
            discount_amount: 2500.00,
            tax_amount: 1125.00,
            total_amount: 23625.00,
            notes: '客戶要求加急處理，已確認所有配件相容性',
            items: [
                {
                    item_name: 'Intel Core i7-13700K',
                    category: 'CPU',
                    item_description: '第13代Intel處理器，16核心24線程',
                    quantity: 1,
                    unit_price: 12000.00,
                    total_price: 12000.00
                },
                {
                    item_name: 'ASUS ROG STRIX RTX 4070',
                    category: 'GPU',
                    item_description: '12GB GDDR6X 顯示記憶體',
                    quantity: 1,
                    unit_price: 18000.00,
                    total_price: 18000.00
                },
                {
                    item_name: 'Corsair Vengeance LPX 32GB',
                    category: 'RAM',
                    item_description: 'DDR4-3200 (2x16GB)',
                    quantity: 1,
                    unit_price: 3500.00,
                    total_price: 3500.00
                },
                {
                    item_name: 'Samsung 980 PRO 1TB',
                    category: 'Storage',
                    item_description: 'NVMe M.2 SSD',
                    quantity: 1,
                    unit_price: 4500.00,
                    total_price: 4500.00
                }
            ]
        };

        function showTestModal() {
            const receipt = testReceipt;
            
            // 創建模態框 HTML
            const modalHtml = `
                <div class="modal fade" id="receiptDetailsModal" tabindex="-1" aria-labelledby="receiptDetailsModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-xl receipt-details-modal">
                        <div class="modal-content receipt-details-content">
                            <div class="modal-header receipt-details-header">
                                <div class="receipt-header-info">
                                    <h4 class="modal-title receipt-title" id="receiptDetailsModalLabel">
                                        <span class="receipt-icon">📄</span>
                                        <span class="receipt-title-text">收據詳情</span>
                                    </h4>
                                    <div class="receipt-number-badge">
                                        <span class="receipt-number-label">收據編號</span>
                                        <span class="receipt-number-value">${receipt.receipt_number}</span>
                                    </div>
                                </div>
                                <button type="button" class="btn-close receipt-close-btn" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body receipt-details-body">
                                <div class="receipt-content-grid">
                                    <!-- Customer Information Card -->
                                    <div class="receipt-info-card customer-info-card">
                                        <div class="card-header-modern">
                                            <div class="card-icon customer-icon">👤</div>
                                            <h5 class="card-title-modern">客戶資訊</h5>
                                        </div>
                                        <div class="card-body-modern">
                                            <div class="info-row">
                                                <span class="info-label">姓名</span>
                                                <span class="info-value">${receipt.customer_name || '未提供'}</span>
                                            </div>
                                            <div class="info-row">
                                                <span class="info-label">電話</span>
                                                <span class="info-value">${receipt.customer_phone || '未提供'}</span>
                                            </div>
                                            <div class="info-row">
                                                <span class="info-label">電子郵件</span>
                                                <span class="info-value">${receipt.customer_email || '未提供'}</span>
                                            </div>
                                            <div class="info-row">
                                                <span class="info-label">地址</span>
                                                <span class="info-value">${receipt.customer_address || '未提供'}</span>
                                            </div>
                                            ${receipt.notes ? `
                                            <div class="info-row">
                                                <span class="info-label">備註</span>
                                                <span class="info-value">${receipt.notes}</span>
                                            </div>
                                            ` : ''}
                                        </div>
                                    </div>

                                    <!-- Receipt Information Card -->
                                    <div class="receipt-info-card receipt-summary-card">
                                        <div class="card-header-modern">
                                            <div class="card-icon receipt-icon">🧾</div>
                                            <h5 class="card-title-modern">收據資訊</h5>
                                        </div>
                                        <div class="card-body-modern">
                                            <div class="info-row">
                                                <span class="info-label">建立日期</span>
                                                <span class="info-value">${new Date(receipt.created_at).toLocaleDateString('zh-TW', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric',
                                                    hour: '2-digit',
                                                    minute: '2-digit'
                                                })}</span>
                                            </div>
                                            <div class="info-row">
                                                <span class="info-label">付款方式</span>
                                                <span class="payment-method-badge">${receipt.payment_method || '現金'}</span>
                                            </div>
                                            <div class="financial-summary">
                                                <div class="financial-row">
                                                    <span class="financial-label">小計</span>
                                                    <span class="financial-value">$${parseFloat(receipt.subtotal || 0).toFixed(2)}</span>
                                                </div>
                                                ${receipt.discount_amount > 0 ? `
                                                <div class="financial-row discount-row">
                                                    <span class="financial-label">折扣</span>
                                                    <span class="financial-value discount-value">-$${parseFloat(receipt.discount_amount).toFixed(2)}</span>
                                                </div>
                                                ` : ''}
                                                ${receipt.tax_amount > 0 ? `
                                                <div class="financial-row">
                                                    <span class="financial-label">稅額</span>
                                                    <span class="financial-value">$${parseFloat(receipt.tax_amount).toFixed(2)}</span>
                                                </div>
                                                ` : ''}
                                                <div class="financial-row total-row">
                                                    <span class="financial-label total-label">總計</span>
                                                    <span class="financial-value total-value">$${parseFloat(receipt.total_amount).toFixed(2)}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Items Section -->
                                <div class="receipt-info-card items-card">
                                    <div class="card-header-modern">
                                        <div class="card-icon items-icon">🖥️</div>
                                        <h5 class="card-title-modern">商品明細 (${receipt.items ? receipt.items.length : 0} 項商品)</h5>
                                    </div>
                                    <div class="card-body-modern">
                                        ${receipt.items && receipt.items.length > 0 ? `
                                            <div class="items-table-container">
                                                <table class="items-table">
                                                    <thead>
                                                        <tr>
                                                            <th>商品名稱</th>
                                                            <th>類別</th>
                                                            <th>描述</th>
                                                            <th class="text-center">數量</th>
                                                            <th class="text-end">單價</th>
                                                            <th class="text-end">小計</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        ${receipt.items.map(item => `
                                                            <tr class="item-row">
                                                                <td class="item-name">${item.item_name}</td>
                                                                <td class="item-category">
                                                                    ${item.category ? `<span class="category-badge">${item.category}</span>` : '<span class="no-category">未分類</span>'}
                                                                </td>
                                                                <td class="item-description">${item.item_description || '無描述'}</td>
                                                                <td class="item-quantity text-center">${item.quantity}</td>
                                                                <td class="item-price text-end">$${parseFloat(item.unit_price).toFixed(2)}</td>
                                                                <td class="item-total text-end">$${parseFloat(item.total_price).toFixed(2)}</td>
                                                            </tr>
                                                        `).join('')}
                                                    </tbody>
                                                </table>
                                            </div>
                                        ` : `
                                            <div class="empty-items-state">
                                                <div class="empty-icon">📦</div>
                                                <p class="empty-text">此收據沒有商品項目</p>
                                            </div>
                                        `}
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer receipt-details-footer">
                                <button type="button" class="btn-modern btn-edit" onclick="alert('編輯功能測試')">
                                    <span class="btn-icon">✏️</span>
                                    <span class="btn-text">編輯收據</span>
                                </button>
                                <button type="button" class="btn-modern btn-close-modal" data-bs-dismiss="modal">
                                    <span class="btn-icon">❌</span>
                                    <span class="btn-text">關閉</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除現有的模態框
            const existingModal = document.getElementById('receiptDetailsModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加模態框到頁面
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 顯示模態框
            const modal = new bootstrap.Modal(document.getElementById('receiptDetailsModal'));
            modal.show();

            // 清理模態框
            document.getElementById('receiptDetailsModal').addEventListener('hidden.bs.modal', function () {
                this.remove();
            });
        }
    </script>
</body>
</html>
